Docmate 全面重构计划书

1. 重构目标与核心理念

本次重构旨在解决当前代码库中存在的冗余、不一致和可维护性差的核心问题。我们将遵循两大核心理念：

统一化 (Unification): 将所有相似的功能（AI 调用、数据解析、UI 展示）归纳为统一的模式，使用同一套代码逻辑处理。
简化 (Simplification): 消除不必要的复杂性，通过引入清晰的状态管理和组件模型，使代码逻辑更直观、更易于理解。
最终目标是获得一个高内聚、低耦合、易于扩展和维护的全新代码架构，并显著提升用户体验的一致性。

2. 详细重构方案与实施路径

我们将分两个阶段进行重构：首先是后端与数据模型的统一，其次是前端 UI 与交互的统一。

阶段一：后端服务与数据模型统一化
目标: 建立一个单一、权威的数据处理流水线。

涉及文件:

packages/shared/src/types.ts
packages/extension/src/services/FrontendAIService.ts
packages/utils/src/services/TerminologyService.ts
修改详情:

统一数据模型 (types.ts):

修改前逻辑: 每个 AI 功能（check, polish, rewrite）都有自己独立的 Result 类型，如 CheckResult, RewriteResult，结构各异。
修改后逻辑:
新增一个通用的 AIResult 接口，它将包含所有功能都可能用到的字段，如 type, originalText, modifiedText, diffs, 以及可选的 issues, summary, explanation。
废弃所有旧的、独立的 Result 类型，彻底消除数据模型的不一致性。
最终结果: 全项目只有一种 AI 结果数据结构，成为唯一的“事实标准”。
统一响应解析器 (FrontendAIService.ts):

修改前逻辑: 每个 AI 服务方法（如 check, polish）在内部调用各自的解析函数（如 parseCheckResponse, parseRewriteResponse）。
修改后逻辑:
新增一个通用的 private parseAIResponse(...) 函数。此函数接收 AI 返回的原始字符串和功能类型，负责提取 JSON 并将其映射到统一的 AIResult 模型。
重构 check, polish, rewrite, translate 等所有公共方法，使其内部统一调用 parseAIResponse。
删除所有旧的、独立的解析函数，如 parseCheckResponse, parseRewriteResponse 等。
最终结果: JSON 解析逻辑被集中到一个地方，极大地减少了代码冗余，修复 bug 或调整格式只需修改一个函数。
增强术语服务 (TerminologyService.ts):

修改前逻辑: 翻译功能完全依赖 AI 自觉保留术语，没有客户端的强制校正。
修改后逻辑:
在 TerminologyService 中新增一个 public replace(text: string): string 方法。该方法使用正则表达式高效地对输入文本进行术语强制替换。
在 FrontendAIService.ts 的 translate 方法中，获取到 AI 的翻译结果后，增加一个步骤：调用 TerminologyService.replace() 对结果进行处理，然后再返回。
最终结果: 实现了可靠的、客户端的术语校正，确保了翻译结果的准确性，不再受 AI 模型表现波动的影响。
阶段二：前端 UI 与交互体验统一化
目标: 建立一个由状态驱动的、组件化的、体验一致的 UI 层。

涉及文件:

packages/ui/src/App.tsx
packages/ui/src/components/CompactHeader.tsx
packages/ui/src/components/ConfigProvider.tsx
创建: packages/ui/src/components/UnifiedResultCard.tsx
删除: packages/ui/src/components/ResultCard.tsx
修改详情:

引入 UI 视图状态 (App.tsx):

修改前逻辑: UI 的显示逻辑与配置是否存在（isConfigured）等多个状态耦合，难以主动控制。
修改后逻辑:
在 App 组件的 state 中新增一个核心状态 view: 'chat' | 'config'，用于显式控制当前应显示“聊天”视图还是“配置”视图。
新增一个 navigateTo(view) 的辅助函数来切换此状态。
重构 render 逻辑，使其只依赖 state.view 来决定渲染哪个主组件。
最终结果: UI 的导航逻辑变得清晰、可预测，为实现任意跳转打下基础。
提供永久的配置入口 (CompactHeader.tsx & ConfigProvider.tsx):

修改前逻辑: 配置页面仅在初次使用时自动弹出，之后没有再次进入的入口。
修改后逻辑:
在 CompactHeader.tsx（聊天窗口的头部）中新增一个“设置”图标按钮，其 onClick 事件会调用从 App.tsx 传入的 navigateTo('config')。
在 ConfigProvider.tsx（配置页面）中新增一个“返回”或“保存并关闭”按钮，其 onClick 事件会调用从 App.tsx 传入的 navigateTo('chat')。
最终结果: 用户可以随时在聊天界面和配置界面之间自由切换，极大地改善了可用性。
统一结果展示组件 (UnifiedResultCard.tsx):

修改前逻辑: 使用一个庞大而复杂的 ResultCard.tsx 组件，内部用大量的 if/else 来处理不同功能的结果展示。
修改后逻辑:
创建一个全新的 UnifiedResultCard.tsx 组件。它只接收一个 result: AIResult 作为 prop。
该组件内部使用声明式的方式，根据 result.type 和 result 中字段的存在与否，来组合渲染不同的子组件（如差异视图、问题列表、总结说明等）。
删除旧的 ResultCard.tsx 文件，并更新 App.tsx 以使用新的 UnifiedResultCard。
最终结果: UI 展示逻辑被分解到更小、更纯粹的组件中，代码更清晰、更易于管理。所有 AI 功能的结果卡片在视觉和交互上保持高度一致。 3. 实现的最终功能与效果

完成本次重构后，项目将达成以下效果：

代码层面:

高可维护性: 修改或新增一种 AI 功能，只需在 types.ts 中扩展类型，在 parseAIResponse 中增加一个分支，并在 UnifiedResultCard 中增加一个渲染条件，影响范围极小。
低冗余: 共享的数据模型、解析器和 UI 组件消除了大量重复代码。
健壮性: 统一的解析和验证流程，以及客户端的术语校正，使系统对 AI 返回格式的错误和表现波动有更强的容错能力。
用户层面:

体验一致: 所有功能的结果展示卡片在布局、交互和视觉风格上完全统一。
配置便捷: 用户可以随时通过顶部的设置按钮进入配置页面，修改配置后也能方便地返回。
结果可靠: 翻译功能的术语将永远保持正确。
